import { Node, Edge } from '@xyflow/react';

// B+树节点接口
interface BPlusTreeNode {
  id: string;
  keys: (number | string | null)[];
  pointers: (string | null)[];
  isLeaf: boolean;
  level: number;
  parent?: string | null;
}

// React Flow节点数据接口
interface BPlusNodeData {
  keys: (number | string | null)[];
  pointers: (string | null)[];
  isLeaf: boolean;
  level: number;
}

// B+树类
class BPlusTree {
  private order: number;
  private root: BPlusTreeNode | null = null;
  private nodeCounter = 0;
  private leafNodes: BPlusTreeNode[] = [];

  constructor(order: number) {
    this.order = order;
  }

  // 创建新节点
  private createNode(isLeaf: boolean, level: number): BPlusTreeNode {
    const node: BPlusTreeNode = {
      id: `node-${this.nodeCounter++}`,
      keys: new Array(this.order - 1).fill(null),
      pointers: new Array(this.order).fill(null),
      isLeaf,
      level,
      parent: null
    };
    
    if (isLeaf) {
      this.leafNodes.push(node);
      // 连接叶子节点的兄弟指针
      if (this.leafNodes.length > 1) {
        const prevLeaf = this.leafNodes[this.leafNodes.length - 2];
        prevLeaf.pointers[this.order - 1] = node.id;
      }
    }
    
    return node;
  }

  // 查找叶子节点
  private findLeaf(key: number | string): BPlusTreeNode {
    if (!this.root) {
      this.root = this.createNode(true, 0);
      return this.root;
    }

    let current = this.root;
    while (!current.isLeaf) {
      let i = 0;
      while (i < current.keys.length && current.keys[i] !== null && key >= current.keys[i]!) {
        i++;
      }
      const childId = current.pointers[i];
      if (!childId) break;
      
      current = this.findNodeById(childId)!;
    }
    
    return current;
  }

  // 根据ID查找节点
  private findNodeById(id: string): BPlusTreeNode | null {
    const allNodes = this.getAllNodes();
    return allNodes.find(node => node.id === id) || null;
  }

  // 获取所有节点
  private getAllNodes(): BPlusTreeNode[] {
    const nodes: BPlusTreeNode[] = [];
    const visited = new Set<string>();
    
    const traverse = (node: BPlusTreeNode | null) => {
      if (!node || visited.has(node.id)) return;
      visited.add(node.id);
      nodes.push(node);
      
      if (!node.isLeaf) {
        for (const pointerId of node.pointers) {
          if (pointerId) {
            const childNode = this.findNodeInTree(pointerId);
            if (childNode) traverse(childNode);
          }
        }
      }
    };
    
    traverse(this.root);
    return nodes;
  }

  // 在树中查找节点（递归辅助函数）
  private findNodeInTree(id: string, node: BPlusTreeNode | null = this.root): BPlusTreeNode | null {
    if (!node) return null;
    if (node.id === id) return node;
    
    if (!node.isLeaf) {
      for (const pointerId of node.pointers) {
        if (pointerId) {
          const found = this.findNodeInTree(id, this.findNodeInTree(pointerId));
          if (found) return found;
        }
      }
    }
    return null;
  }

  // 在叶子节点中插入键
  private insertIntoLeaf(leaf: BPlusTreeNode, key: number | string): void {
    let i = 0;
    while (i < leaf.keys.length && leaf.keys[i] !== null && key > leaf.keys[i]!) {
      i++;
    }
    
    // 插入键
    for (let j = leaf.keys.length - 1; j > i; j--) {
      leaf.keys[j] = leaf.keys[j - 1];
    }
    leaf.keys[i] = key;
  }

  // 检查节点是否已满
  private isNodeFull(node: BPlusTreeNode): boolean {
    return node.keys.filter(k => k !== null).length >= this.order - 1;
  }

  // 分裂叶子节点
  private splitLeaf(leaf: BPlusTreeNode): BPlusTreeNode {
    const newLeaf = this.createNode(true, leaf.level);
    const mid = Math.ceil((this.order - 1) / 2);
    
    // 移动键到新叶子节点
    for (let i = mid; i < this.order - 1; i++) {
      newLeaf.keys[i - mid] = leaf.keys[i];
      leaf.keys[i] = null;
    }
    
    // 更新兄弟指针
    newLeaf.pointers[this.order - 1] = leaf.pointers[this.order - 1];
    leaf.pointers[this.order - 1] = newLeaf.id;
    
    return newLeaf;
  }

  // 分裂内部节点
  private splitInternal(node: BPlusTreeNode): { newNode: BPlusTreeNode; promotedKey: number | string } {
    const newNode = this.createNode(false, node.level);
    const mid = Math.floor(this.order / 2);
    const promotedKey = node.keys[mid]!;
    
    // 移动键和指针到新节点
    for (let i = mid + 1; i < this.order - 1; i++) {
      newNode.keys[i - mid - 1] = node.keys[i];
      node.keys[i] = null;
    }
    
    for (let i = mid + 1; i < this.order; i++) {
      newNode.pointers[i - mid - 1] = node.pointers[i];
      node.pointers[i] = null;
    }
    
    // 清除被提升的键
    node.keys[mid] = null;
    
    return { newNode, promotedKey };
  }

  // 更新所有节点的层级
  private updateLevels(): void {
    const updateLevel = (node: BPlusTreeNode | null, level: number) => {
      if (!node) return;
      node.level = level;
      
      if (!node.isLeaf) {
        for (const pointerId of node.pointers) {
          if (pointerId) {
            const child = this.findNodeInTree(pointerId);
            if (child) updateLevel(child, level - 1);
          }
        }
      }
    };
    
    if (this.root) {
      updateLevel(this.root, this.getTreeHeight() - 1);
    }
  }

  // 获取树的高度
  private getTreeHeight(): number {
    const getHeight = (node: BPlusTreeNode | null): number => {
      if (!node) return 0;
      if (node.isLeaf) return 1;
      
      let maxHeight = 0;
      for (const pointerId of node.pointers) {
        if (pointerId) {
          const child = this.findNodeInTree(pointerId);
          if (child) {
            maxHeight = Math.max(maxHeight, getHeight(child));
          }
        }
      }
      return maxHeight + 1;
    };
    
    return getHeight(this.root);
  }

  // 插入到父节点
  private insertIntoParent(left: BPlusTreeNode, key: number | string, right: BPlusTreeNode): void {
    if (left === this.root) {
      // 创建新根
      const newRoot = this.createNode(false, left.level + 1);
      newRoot.keys[0] = key;
      newRoot.pointers[0] = left.id;
      newRoot.pointers[1] = right.id;
      left.parent = newRoot.id;
      right.parent = newRoot.id;
      this.root = newRoot;

      // 更新所有节点的层级
      this.updateLevels();
      return;
    }

    const parent = this.findNodeById(left.parent!)!;

    // 在父节点中找到插入位置
    let i = 0;
    while (i < parent.keys.length && parent.keys[i] !== null && key > parent.keys[i]!) {
      i++;
    }

    // 插入键和指针
    for (let j = parent.keys.length - 1; j > i; j--) {
      parent.keys[j] = parent.keys[j - 1];
    }
    for (let j = parent.pointers.length - 1; j > i + 1; j--) {
      parent.pointers[j] = parent.pointers[j - 1];
    }

    parent.keys[i] = key;
    parent.pointers[i + 1] = right.id;
    right.parent = parent.id;

    // 检查父节点是否需要分裂
    if (this.isNodeFull(parent)) {
      const { newNode, promotedKey } = this.splitInternal(parent);
      this.insertIntoParent(parent, promotedKey, newNode);
    }
  }

  // 插入键
  public insert(key: number | string): void {
    const leaf = this.findLeaf(key);

    // 检查键是否已存在
    if (leaf.keys.includes(key)) {
      return; // 键已存在，不插入
    }

    this.insertIntoLeaf(leaf, key);

    // 检查叶子节点是否需要分裂
    if (this.isNodeFull(leaf)) {
      const newLeaf = this.splitLeaf(leaf);
      const promotedKey = newLeaf.keys.find(k => k !== null)!;
      this.insertIntoParent(leaf, promotedKey, newLeaf);
    }
  }

  // 获取根节点
  public getRoot(): BPlusTreeNode | null {
    return this.root;
  }

  // 转换为React Flow数据
  public toReactFlowData(): { nodes: Node<BPlusNodeData>[], edges: Edge[] } {
    const nodes: Node<BPlusNodeData>[] = [];
    const edges: Edge[] = [];

    if (!this.root) {
      return { nodes, edges };
    }

    const allNodes = this.getAllNodes();

    // 创建React Flow节点
    allNodes.forEach(node => {
      nodes.push({
        id: node.id,
        type: node.isLeaf ? 'bPlusLeafNode' : 'bPlusInternalNode',
        position: { x: 0, y: 0 }, // 初始位置，稍后由布局算法设置
        data: {
          keys: [...node.keys],
          pointers: [...node.pointers],
          isLeaf: node.isLeaf,
          level: node.level
        }
      });
    });

    // 创建React Flow边
    allNodes.forEach(node => {
      if (!node.isLeaf) {
        // 内部节点的子节点连接
        node.pointers.forEach((pointerId, index) => {
          if (pointerId) {
            edges.push({
              id: `${node.id}-${pointerId}`,
              source: node.id,
              target: pointerId,
              sourceHandle: `pointer-${index}`,
              targetHandle: 'top',
              type: 'default'
            });
          }
        });
      } else {
        // 叶子节点的兄弟连接
        const siblingId = node.pointers[this.order - 1];
        if (siblingId) {
          edges.push({
            id: `${node.id}-sibling-${siblingId}`,
            source: node.id,
            target: siblingId,
            sourceHandle: `sibling`,
            targetHandle: 'sibling-target',
            type: 'default',
            style: { stroke: '#999', strokeDasharray: '5,5' }
          });
        }
      }
    });

    return { nodes, edges };
  }
}

// 主要导出函数
export function bPlusTreeToReactFlow(
  keys: (number | string)[],
  order: number
): { nodes: Node<BPlusNodeData>[], edges: Edge[] } {
  const tree = new BPlusTree(order);

  // 按顺序插入所有键
  keys.forEach(key => {
    tree.insert(key);
  });

  return tree.toReactFlowData();
}

// 导出类型
export type { BPlusNodeData };
