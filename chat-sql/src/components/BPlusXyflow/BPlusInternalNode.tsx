import React from 'react';
import { Handle, Position, NodeProps } from '@xyflow/react';
import styles from './BPlusTreeVisualizer.module.css';

interface BPlusNodeData {
  keys: (number | string | null)[];
  pointers: (string | null)[];
  isLeaf: boolean;
  level: number;
}

const BPlusInternalNode: React.FC<NodeProps> = ({ data }) => {
  const nodeData = data as unknown as BPlusNodeData;
  const { keys, pointers } = nodeData;

  // 计算实际的指针数量
  const actualPointers = pointers.filter(p => p !== null);
  
  return (
    <div className={styles['bplus-internal-node']}>
      {/* 顶部连接点 */}
      <Handle
        type="target"
        position={Position.Top}
        id="top"
        className={`${styles['bplus-handle']} ${styles['bplus-handle-target']}`}
      />

      <div className={styles['bplus-node-content']}>
        <div className={styles['bplus-internal-layout']}>
          {/* 第一个指针位置（最左边） */}
          <div className={styles['bplus-pointer-position']}>
            {actualPointers[0] && (
              <Handle
                type="source"
                position={Position.Bottom}
                id="pointer-0"
                className={`${styles['bplus-handle']} ${styles['bplus-handle-source']}`}
                style={{ left: '50%', transform: 'translateX(-50%)', bottom: '-8px' }}
              />
            )}
          </div>

          {/* 键和对应的指针位置 */}
          {keys.map((key, index) => (
            <React.Fragment key={index}>
              {/* 键槽位 */}
              <div className={styles['bplus-key-cell']}>
                <div className={styles['bplus-key-content']}>
                  {key !== null ? key : ''}
                </div>
                {/* 键下方的指针连接点 */}
                {pointers[index + 1] && (
                  <Handle
                    type="source"
                    position={Position.Bottom}
                    id={`pointer-${index + 1}`}
                    className={`${styles['bplus-handle']} ${styles['bplus-handle-source']}`}
                    style={{ left: '50%', transform: 'translateX(-50%)', bottom: '-8px' }}
                  />
                )}
              </div>
            </React.Fragment>
          ))}
        </div>
      </div>
    </div>
  );
};

export default BPlusInternalNode;
