'use client';

import React, { useState } from 'react';
import { BPlusTreeVisualizer } from './index';

const SimpleDemo: React.FC = () => {
  const [keys, setKeys] = useState<number[]>([10, 20, 5, 15, 25]);
  const [order, setOrder] = useState<number>(3);

  return (
    <div style={{ width: '100vw', height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <header style={{ 
        padding: '16px', 
        background: '#f5f5f5',
        borderBottom: '1px solid #ddd'
      }}>
        <h1 style={{ margin: '0 0 16px 0' }}>B+树可视化演示</h1>
        <div style={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
          <label>
            阶数 (M): 
            <input
              type="number"
              value={order}
              onChange={(e) => setOrder(Math.max(2, parseInt(e.target.value) || 2))}
              min="2"
              max="10"
              style={{ marginLeft: '8px', width: '60px' }}
            />
          </label>
          <div>
            <strong>当前键:</strong> [{keys.join(', ')}]
          </div>
        </div>
      </header>
      
      <div style={{ flex: 1, position: 'relative' }}>
        <BPlusTreeVisualizer initialKeys={keys} order={order} />
      </div>
    </div>
  );
};

export default SimpleDemo;
