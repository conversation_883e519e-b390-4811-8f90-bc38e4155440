import React from 'react';
import { Handle, Position, NodeProps } from '@xyflow/react';
import styles from './BPlusTreeVisualizer.module.css';

interface BPlusNodeData {
  keys: (number | string | null)[];
  pointers: (string | null)[];
  isLeaf: boolean;
  level: number;
}

const BPlusLeafNode: React.FC<NodeProps> = ({ data }) => {
  const nodeData = data as unknown as BPlusNodeData;
  const { keys, pointers } = nodeData;

  const siblingPointer = pointers[pointers.length - 1]; // 最后一个指针是兄弟指针

  return (
    <div className={styles['bplus-leaf-node']}>
      {/* 顶部连接点 */}
      <Handle
        type="target"
        position={Position.Top}
        id="top"
        className={`${styles['bplus-handle']} ${styles['bplus-handle-target']}`}
      />

      {/* 兄弟节点连接点 */}
      <Handle
        type="target"
        position={Position.Left}
        id="sibling-target"
        className={`${styles['bplus-handle']} ${styles['bplus-handle-sibling']}`}
        style={{ top: '50%' }}
      />

      <div className={styles['bplus-node-content']}>
        <div className={styles['bplus-leaf-layout']}>
          {/* 只显示键，不显示值 */}
          <div className={styles['bplus-key-row']}>
            {keys.map((key, index) => (
              <div key={index} className={styles['bplus-key-cell']}>
                <div className={styles['bplus-key-content']}>
                  {key !== null ? key : ''}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 兄弟指针 */}
        {siblingPointer && (
          <Handle
            type="source"
            position={Position.Right}
            id="sibling"
            className={`${styles['bplus-handle']} ${styles['bplus-handle-sibling']}`}
            style={{ top: '50%' }}
          />
        )}
      </div>
    </div>
  );
};

export default BPlusLeafNode;
